import { TabLayout } from "@/src/components/layout/TabLayout";
import { useAppTheme } from "@/src/design-system";
import { StatusBar } from 'react-native';
import { EnergyDashboard } from "../energy-usage";

export default function EnergyScreen() {
  const { theme } = useAppTheme();

  return (
    <TabLayout scrollable={false} contentPadding={false}>
      <StatusBar
        barStyle={theme.colors.background === '#FFFFFF' ? 'dark-content' : 'light-content'}
        backgroundColor={theme.colors.background}
      />

      <EnergyDashboard initialPeriod="month" />
    </TabLayout>
  );
}


