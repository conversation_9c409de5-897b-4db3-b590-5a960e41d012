import { useDigitalCore } from "@/src/api/hooks/useDigitalCore";
import { TabLayout } from "@/src/components/layout/TabLayout";
import { useAppTheme } from "@/src/design-system";
import { StatusBar, Text, View } from 'react-native';
import { EnergyDashboard } from "../energy-usage";

export default function EnergyScreen() {
  const { theme } = useAppTheme();
  const { customerId,error } = useDigitalCore();

  return (
    <TabLayout scrollable={false} contentPadding={false}>
      <StatusBar
        barStyle={theme.colors.background === '#FFFFFF' ? 'dark-content' : 'light-content'}
        backgroundColor={theme.colors.background}
      />

      {/* Debug info */}
      <View style={{ padding: 10, backgroundColor: '#f0f0f0', margin: 10 }}>
        <Text>Customer ID: {customerId || 'Not available'}</Text>
        {error && <Text style={{ color: 'red' }}>Error: {error.message}</Text>}
      </View>

      <EnergyDashboard initialPeriod="month" />
    </TabLayout>
  );
}


