/**
 * Utility functions for transforming API data for the Energy Usage Dashboard
 */

import { ChartDataPoint, EnergyApiResponse, EnergySummary, TimePeriod } from '../types/api';
import { formatDate } from './formatters';

/**
 * Helper function to safely get a numeric value
 * Handles both plain numbers and DecimalObject structures
 */
const safeNumber = (value: any): number => {
  // Handle DecimalObject structure
  if (value && typeof value === 'object' && 'value' in value) {
    const decimalValue = value.value;
    if (typeof decimalValue === 'number' && !isNaN(decimalValue) && isFinite(decimalValue)) {
      return decimalValue;
    }
    if (typeof decimalValue === 'string') {
      const parsed = parseFloat(decimalValue);
      if (!isNaN(parsed) && isFinite(parsed)) {
        return parsed;
      }
    }
    return 0;
  }

  // Handle plain numbers
  if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
    return value;
  }

  // Handle string numbers
  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    if (!isNaN(parsed) && isFinite(parsed)) {
      return parsed;
    }
  }

  return 0;
};

/**
 * Transform API response data into chart data points
 * @param apiResponse The API response data
 * @param period The time period
 * @returns Array of chart data points
 */
export const transformApiDataToChartData = (
  apiResponse: EnergyApiResponse,
  period: TimePeriod
): ChartDataPoint[] => {
  console.log('transformApiDataToChartData called with:', apiResponse);

  if (!apiResponse?.data?.usages?.length) {
    console.log('No usages data found in API response');
    return [];
  }

  const usage = apiResponse.data.usages[0];
  console.log('Processing usage data:', usage);
  console.log('Number of entries:', usage.entries?.length);

  return usage.entries.map(entry => {
    const { actual, previousYear, weather } = entry;
    const formattedDate = formatDate(actual.date, period);

    return {
      name: formattedDate,
      date: actual.date,
      electricity: safeNumber(actual.electricity?.high),
      gas: safeNumber(actual.gas?.high),
      electricityCost: safeNumber(actual.electricity?.totalCostInclVat),
      gasCost: safeNumber(actual.gas?.totalCostInclVat),
      totalCost: safeNumber(actual.totalCostInclVat),
      previousYearElectricity: safeNumber(previousYear?.electricity?.high),
      previousYearGas: safeNumber(previousYear?.gas?.high),
      previousYearElectricityCost: safeNumber(previousYear?.electricity?.totalCostInclVat),
      previousYearGasCost: safeNumber(previousYear?.gas?.totalCostInclVat),
      previousYearTotalCost: safeNumber(previousYear?.totalCostInclVat),
      weather: weather ? {
        temp: safeNumber(weather.temp),
        sunshine: safeNumber(weather.sunshine),
      } : undefined,
    };
  });
};

/**
 * Extract summary data from the API response
 * @param apiResponse The API response data
 * @returns Summary data object
 */
export const extractSummaryData = (apiResponse: EnergyApiResponse): EnergySummary | null => {
  if (!apiResponse?.data?.usages?.length) {
    return null;
  }

  const usage = apiResponse.data.usages[0];
  const { period, summary, entries } = usage;
  
  // Calculate weather averages if available
  let weatherSummary = undefined;
  
  const entriesWithWeather = entries.filter(entry => entry.weather);
  if (entriesWithWeather.length > 0) {
    const totalTemp = entriesWithWeather.reduce((sum, entry) => sum + safeNumber(entry.weather?.temp), 0);
    const totalSunshine = entriesWithWeather.reduce((sum, entry) => sum + safeNumber(entry.weather?.sunshine), 0);

    // Find coldest and warmest days
    let coldestDay = entriesWithWeather[0].weather;
    let warmestDay = entriesWithWeather[0].weather;

    entriesWithWeather.forEach(entry => {
      if (entry.weather && safeNumber(entry.weather.temp) < safeNumber(coldestDay?.temp)) {
        coldestDay = entry.weather;
      }
      if (entry.weather && safeNumber(entry.weather.temp) > safeNumber(warmestDay?.temp)) {
        warmestDay = entry.weather;
      }
    });

    weatherSummary = {
      averageTemp: safeNumber(totalTemp / entriesWithWeather.length),
      sunshine: safeNumber(totalSunshine / entriesWithWeather.length),
      coldestDay: coldestDay?.coldestDay || { date: '', temp: 0 },
      warmestDay: warmestDay?.warmestDay || { date: '', temp: 0 },
    };
  }
  
  return {
    period: {
      from: period.from,
      to: period.to,
    },
    electricity: {
      consumption: safeNumber(summary.aggregationTotals.electricity?.high),
      cost: safeNumber(summary.aggregationTotals.electricity?.totalCostInclVat),
      unit: 'kWh',
    },
    gas: {
      consumption: safeNumber(summary.aggregationTotals.gas?.high),
      cost: safeNumber(summary.aggregationTotals.gas?.totalCostInclVat),
      unit: 'm³',
    },
    total: {
      cost: safeNumber(summary.aggregationTotals.electricity?.totalCostInclVat) +
            safeNumber(summary.aggregationTotals.gas?.totalCostInclVat),
    },
    weather: weatherSummary,
  };
};

/**
 * Generate energy-saving tips based on usage patterns
 * @param summaryData The summary data
 * @returns Array of energy-saving tips
 */
export const generateEnergySavingTips = (summaryData: EnergySummary | null): string[] => {
  if (!summaryData) {
    return [
      'Turn off lights when not in use',
      'Unplug devices when not in use',
      'Use energy-efficient appliances',
      'Lower your thermostat by 1°C to save up to 10% on heating costs',
    ];
  }

  const tips: string[] = [];

  // Add electricity tips
  if (summaryData.electricity.consumption > 0) {
    tips.push('Switch to LED bulbs to save up to 80% on lighting costs');
    tips.push('Use smart power strips to eliminate phantom energy use');
  }

  // Add gas tips
  if (summaryData.gas.consumption > 0) {
    tips.push('Insulate your home to reduce heating costs');
    tips.push('Service your heating system regularly for optimal efficiency');
  }

  // Add weather-based tips
  if (summaryData.weather) {
    if (summaryData.weather.averageTemp < 10) {
      tips.push('Use draft stoppers to prevent heat loss through doors and windows');
    } else if (summaryData.weather.averageTemp > 20) {
      tips.push('Use fans instead of air conditioning to save energy');
    }
  }

  // If we don't have enough tips, add some general ones
  if (tips.length < 3) {
    tips.push('Install a programmable thermostat to optimize heating and cooling');
    tips.push('Wash clothes in cold water to save on water heating costs');
  }

  return tips;
};

/**
 * Transform Digital Core API response to Energy API format
 * @param dcResponse The Digital Core API response
 * @param period The time period
 * @returns Transformed data in Energy API format
 */
export const transformDigitalCoreToEnergyApi = (
  dcResponse: any, // Using any for now since we need to import the type
  period: TimePeriod
): EnergyApiResponse => {
  console.log('transformDigitalCoreToEnergyApi called with:', dcResponse);

  // Helper function to extract decimal value safely
  const getDecimalValue = (decimal: any): number => {
    if (!decimal || typeof decimal !== 'object') {
      return 0;
    }

    const value = decimal.value;

    // Check for valid number
    if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
      return value;
    }

    // Try to parse as number if it's a string
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      if (!isNaN(parsed) && isFinite(parsed)) {
        return parsed;
      }
    }

    return 0;
  };

  // Get the first usage aggregation
  const usage = dcResponse.data?.usages?.[0];
  console.log('Usage aggregation:', usage);

  if (!usage || !usage.entries) {
    console.log('No usage data or entries found');
    // Return empty structure if no data
    return {
      data: {
        metadata: {
          interval: period,
          aggregation: period,
        },
        usages: [],
      },
    };
  }

  console.log('Found', usage.entries.length, 'entries to transform');

  // Transform entries
  const transformedEntries = (usage.entries || []).map((entry: any) => {
    const actual = entry.actual;
    const previousYear = entry.previousYear;
    const weather = entry.weather;

    return {
      actual: {
        date: actual?.date || '',
        gas: actual?.gas ? {
          status: actual.gas.status || 'MEASURED',
          high: getDecimalValue(actual.gas.high),
          highCostInclVat: getDecimalValue(actual.gas.highCostInclVat),
          totalCostInclVat: getDecimalValue(actual.gas.totalCostInclVat),
          fixedCostInclVat: getDecimalValue(actual.gas.fixedCostInclVat),
        } : undefined,
        electricity: actual?.electricity ? {
          status: actual.electricity.status || 'MEASURED',
          high: getDecimalValue(actual.electricity.high),
          highCostInclVat: getDecimalValue(actual.electricity.highCostInclVat),
          totalCostInclVat: getDecimalValue(actual.electricity.totalCostInclVat),
          fixedCostInclVat: getDecimalValue(actual.electricity.fixedCostInclVat),
        } : undefined,
        totalUsageCostInclVat: getDecimalValue(actual?.totalUsageCostInclVat),
        totalFixedCostInclVat: getDecimalValue(actual?.totalFixedCostInclVat),
        totalCostInclVat: getDecimalValue(actual?.totalCostInclVat),
      },
      previousYear: previousYear ? {
        date: previousYear.date || '',
        gas: previousYear.gas ? {
          high: getDecimalValue(previousYear.gas.high),
          highCostInclVat: getDecimalValue(previousYear.gas.highCostInclVat),
          totalCostInclVat: getDecimalValue(previousYear.gas.totalCostInclVat),
        } : undefined,
        electricity: previousYear.electricity ? {
          high: getDecimalValue(previousYear.electricity.high),
          highCostInclVat: getDecimalValue(previousYear.electricity.highCostInclVat),
          totalCostInclVat: getDecimalValue(previousYear.electricity.totalCostInclVat),
        } : undefined,
        totalUsageCostInclVat: getDecimalValue(previousYear.totalUsageCostInclVat),
        totalFixedCostInclVat: getDecimalValue(previousYear.totalFixedCostInclVat),
        totalCostInclVat: getDecimalValue(previousYear.totalCostInclVat),
      } : undefined,
      weather: weather ? {
        date: weather.date || '',
        temp: getDecimalValue(weather.temp),
        sunshine: getDecimalValue(weather.sunshine),
        coldestDay: weather.coldestDay ? {
          date: weather.coldestDay.date || '',
          temp: getDecimalValue(weather.coldestDay.temp),
        } : { date: '', temp: 0 },
        warmestDay: weather.warmestDay ? {
          date: weather.warmestDay.date || '',
          temp: getDecimalValue(weather.warmestDay.temp),
        } : { date: '', temp: 0 },
      } : undefined,
    };
  });

  // Transform summary
  const summary = usage.summary?.aggregationTotals;

  return {
    data: {
      metadata: {
        interval: dcResponse.data?.metadata?.interval || period,
        aggregation: dcResponse.data?.metadata?.aggregation || period,
      },
      usages: [{
        period: {
          from: usage.period?.from || '',
          to: usage.period?.to || '',
        },
        entries: transformedEntries,
        summary: {
          aggregationTotals: {
            gas: summary?.gas ? {
              high: getDecimalValue(summary.gas.high),
              highCostInclVat: getDecimalValue(summary.gas.highCostInclVat),
              totalCostInclVat: getDecimalValue(summary.gas.totalCostInclVat),
            } : undefined,
            electricity: summary?.electricity ? {
              high: getDecimalValue(summary.electricity.high),
              highCostInclVat: getDecimalValue(summary.electricity.highCostInclVat),
              totalCostInclVat: getDecimalValue(summary.electricity.totalCostInclVat),
            } : undefined,
          },
        },
      }],
    },
  };
};
