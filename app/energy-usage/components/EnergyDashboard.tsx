/**
 * Energy Usage Dashboard Component
 */

import {
    Text,
    useAppTheme
} from "@/src/design-system";
import React, { useState } from 'react';
import { RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import { useEnergyData } from '../hooks/useEnergyData';
import { TimePeriod } from '../types/api';
// Import components
import { BreakdownCard, TipsCard, TotalCard, WeatherCard } from './cards';
import { ChartControls, EnergyUsageChart } from './charts';
import { PeriodSelector } from './navigation/';

interface EnergyDashboardProps {
  initialPeriod?: TimePeriod;
}

/**
 * Energy Usage Dashboard Component
 * Main component for the Energy Usage tab
 */
export const EnergyDashboard: React.FC<EnergyDashboardProps> = ({
  initialPeriod = 'month',
}) => {
  const { theme } = useAppTheme();
  const [period, setPeriod] = useState<TimePeriod>(initialPeriod);
  const [showComparison, setShowComparison] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // Use the energy data hook
  const {
    isLoading,
    error,
    chartData,
    summaryData,
    energySavingTips,
    displayMode,
    setDisplayMode,
    refreshData,
  } = useEnergyData({
    period,
    showComparison,
  });

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await refreshData();
    setRefreshing(false);
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <Text variant="headlineMedium" style={styles.title}>
        Energy Usage
      </Text>

      {/* Period Selector */}
      <PeriodSelector
        selectedPeriod={period}
        onPeriodChange={setPeriod}
      />

      {/* Total Card */}
      <TotalCard
        summaryData={summaryData}
        period={period}
        isLoading={isLoading}
      />

      {/* Chart Section */}
      <View style={styles.chartSection}>
        <Text variant="titleMedium" style={styles.sectionTitle}>
          Usage Breakdown
        </Text>

        {/* Chart Controls */}
        <ChartControls
          displayMode={displayMode}
          setDisplayMode={setDisplayMode}
          showComparison={showComparison}
          setShowComparison={setShowComparison}
        />

        {/* Debug Info */}
        <View style={{ padding: 10, backgroundColor: '#f0f0f0', marginBottom: 10 }}>
          <Text style={{ fontSize: 12 }}>Chart Data Length: {chartData?.length || 0}</Text>
          <Text style={{ fontSize: 12 }}>Loading: {isLoading.toString()}</Text>
          <Text style={{ fontSize: 12 }}>Error: {error?.message || 'None'}</Text>
          {chartData && chartData.length > 0 && (
            <Text style={{ fontSize: 12 }}>
              First Entry: {JSON.stringify(chartData[0], null, 2).substring(0, 200)}...
            </Text>
          )}
        </View>

        {/* Energy Usage Chart */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text>Loading chart data...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={{ color: theme.colors.error }}>
              Error loading chart data: {error.message}
            </Text>
          </View>
        ) : (
          <EnergyUsageChart
            data={chartData}
            displayMode={displayMode}
            showComparison={showComparison}
          />
        )}
      </View>

      {/* Breakdown Card */}
      <BreakdownCard
        summaryData={summaryData}
        isLoading={isLoading}
      />

      {/* Weather Card (only shown when weather data is available) */}
      <WeatherCard
        summaryData={summaryData}
        isLoading={isLoading}
      />

      {/* Energy Saving Tips */}
      <TipsCard
        tips={energySavingTips}
        isLoading={isLoading}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  title: {
    marginBottom: 16,
  },
  chartSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 8,
  },
  loadingContainer: {
    height: 300, // Increased to match the Victory chart height
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    height: 300, // Increased to match the Victory chart height
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default EnergyDashboard;
