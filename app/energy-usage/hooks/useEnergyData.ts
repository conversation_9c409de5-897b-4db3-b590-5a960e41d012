/**
 * Hook for fetching and processing energy usage data
 */

import { useEffect, useState } from 'react';
import { ChartDataPoint, DisplayMode, EnergyApiResponse, EnergySummary, TimePeriod } from '../types/api';
import { extractSummaryData, generateEnergySavingTips, transformApiDataToChartData, transformDigitalCoreToEnergyApi } from '../utils/dataTransformers';

// Mock API data for development (fallback)
import mockApiResponse from '../mocks/energyApiResponse.json';

interface UseEnergyDataProps {
  period: TimePeriod;
  showComparison?: boolean;
}

interface UseEnergyDataResult {
  isLoading: boolean;
  error: Error | null;
  chartData: ChartDataPoint[];
  summaryData: EnergySummary | null;
  energySavingTips: string[];
  displayMode: DisplayMode;
  setDisplayMode: (mode: DisplayMode) => void;
  refreshData: () => void;
}

/**
 * Hook for fetching and processing energy usage data
 */
export const useEnergyData = ({
  period,
  showComparison = false,
}: UseEnergyDataProps): UseEnergyDataResult => {
  // Import the Digital Core hook at the top of the component
  const { getUsages, getProfile, customerId, isAuthenticated } = require('@/src/api/hooks/useDigitalCore').useDigitalCore();
  const { getDateRangeForPeriod } = require('../utils/dateUtils');

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [summaryData, setSummaryData] = useState<EnergySummary | null>(null);
  const [energySavingTips, setEnergySavingTips] = useState<string[]>([]);
  const [displayMode, setDisplayMode] = useState<DisplayMode>('cost');
  const [accountId, setAccountId] = useState<number | null>(null);

  // Fetch account ID when customer ID is available
  useEffect(() => {
    const fetchAccountId = async () => {
      if (customerId && isAuthenticated && !accountId) {
        try {
          const profile = await getProfile();
          if (profile?.data?.accounts?.length > 0) {
            // Get the first active account
            const activeAccount = profile.data.accounts.find((acc: any) => acc.active);
            if (activeAccount) {
              setAccountId(activeAccount.accountId);
              console.log('Account ID set:', activeAccount.accountId);
            }
          }
        } catch (error) {
          console.error('Error fetching account ID:', error);
        }
      }
    };

    fetchAccountId();
  }, [customerId, isAuthenticated, accountId, getProfile]);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Check if user is authenticated and has customer ID and account ID
      if (!isAuthenticated || !customerId || !accountId) {
        console.warn('User not authenticated, customer ID not available, or account ID not available, using mock data');
        // Use mock data as fallback
        const data = mockApiResponse as EnergyApiResponse;

        // Transform data for charts
        const transformedChartData = transformApiDataToChartData(data, period);
        setChartData(transformedChartData);

        // Extract summary data
        const extractedSummaryData = extractSummaryData(data);
        setSummaryData(extractedSummaryData);

        // Generate energy saving tips
        const tips = generateEnergySavingTips(extractedSummaryData);
        setEnergySavingTips(tips);

        return;
      }

      // Get date range for the selected period
      const dateRange = getDateRangeForPeriod(period);

      // Map period to API aggregation and interval
      const aggregationMap = {
        'hour': { aggregation: 'Hour' as const, interval: 'Hour' as const },
        'day': { aggregation: 'Day' as const, interval: 'Day' as const },
        'week': { aggregation: 'Week' as const, interval: 'Week' as const },
        'month': { aggregation: 'Month' as const, interval: 'Month' as const },
        'year': { aggregation: 'Year' as const, interval: 'Year' as const },
      };

      const { aggregation, interval } = aggregationMap[period];

      console.log('Fetching energy usage data:', {
        customerId,
        accountId,
        period,
        aggregation,
        interval,
        dateRange,
        showComparison
      });

      // Call the Digital Core API
      const usagesResponse = await getUsages(accountId, {
        aggregation,
        interval,
        start: dateRange.from,
        end: dateRange.to,
        addBudget: true,
        addWeather: true,
        extrapolate: false,
      });

      if (!usagesResponse) {
        throw new Error('No data received from API');
      }

      // Transform the Digital Core API response to match our expected format
      const transformedData = transformDigitalCoreToEnergyApi(usagesResponse, period);

      // Transform data for charts
      const transformedChartData = transformApiDataToChartData(transformedData, period);
      console.log('Final chart data:', transformedChartData);
      setChartData(transformedChartData);

      // Extract summary data
      const extractedSummaryData = extractSummaryData(transformedData);
      setSummaryData(extractedSummaryData);

      // Generate energy saving tips
      const tips = generateEnergySavingTips(extractedSummaryData);
      setEnergySavingTips(tips);

    } catch (err) {
      console.error('Error fetching energy data:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch energy data'));

      // Fallback to mock data on error
      console.warn('Falling back to mock data due to error');
      const data = mockApiResponse as EnergyApiResponse;

      const transformedChartData = transformApiDataToChartData(data, period);
      setChartData(transformedChartData);

      const extractedSummaryData = extractSummaryData(data);
      setSummaryData(extractedSummaryData);

      const tips = generateEnergySavingTips(extractedSummaryData);
      setEnergySavingTips(tips);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when period, comparison, or accountId changes
  useEffect(() => {
    fetchData();
  }, [period, showComparison, accountId]);

  return {
    isLoading,
    error,
    chartData,
    summaryData,
    energySavingTips,
    displayMode,
    setDisplayMode,
    refreshData: fetchData,
  };
};
