/**
 * API Types for Digital Core (DC) API
 */

// Profile API Response
export interface ProfileResponseData {
  customerId: number;
  userAccount: {
    userName: string;
    lastLogin: string;
    primaryEmail?: string;
  };
  customerType: string;
  features: string[];
  contact: {
    name: string;
    firstName?: string;
    gender?: string;
    emailAddress: string;
    preferences: null;
    phoneNumber: string;
    mobilePhoneNumber: string;
    address: {
      street: string;
      postalCode: string;
      houseNumber: string;
      houseNumberSuffix: null | string;
      city: string;
      bus: null | string;
    };
  };
  person: null | {
    firstName: null | string;
    gender: string;
    initials: string;
    name: string;
    salutation: string;
    surname: string;
    surnamePreposition: null | string;
    dateOfBirth: null | string;
  };
  organisation: null | {
    name: string;
    kvk: string;
  };
  accounts: {
    accountId: number;
    address: {
      street: string;
      postalCode: string;
      houseNumber: string;
      houseNumberSuffix: null | string;
      city: string;
      bus: null | string;
    };
    correspondenceAddress: {
      street: string;
      postalCode: string;
      houseNumber: string;
      houseNumberSuffix: null | string;
      city: string;
      bus: null | string;
    };
    active: boolean;
    customerProfileType: string;
    productTypes: string[];
    productTypeDetails: {
      productType: string;
      isDynamicPricing: boolean;
      priceDifferentiation: string;
    }[];
    nextChargeDate: null | string;
    startDate: string;
    endDate: null | string;
    hasRedelivery: boolean;
    hasRedeliveryCostTariff: boolean;
    hasServiceContract: boolean;
    meterDetails: {
      productType: string;
      isSmartMeterReadingAllowed: boolean;
      isSmartMeter: boolean;
    }[];
    classification: null | string;
    hasDynamicPricing: boolean;
  }[];
  orders: null | any;
  outstandingReadings: any[];
  readingInfo: null | any;
  relocationInfo: {
    hasRelocation: boolean;
    moveOutDateSpecified: null | string;
  };
  hasOpenContractRenewal?: boolean;
}

// Wrapper response structure
export interface ProfileResponse {
  data: ProfileResponseData;
}

// Meters Status API Response
export interface MetersStatusResponse {
  meters: {
    utilityType: string;
    gridOperator: string;
    inError: boolean;
    errors: any[];
  }[];
}

// Usages API Response - Updated with real API structure
export type DC_Domain_Models_Usages_UsageStatusType =
  | 'Default'
  | 'HasGasOnMultipleMeters'
  | 'HasElectricityOnMultipleMeters';

export type DC_Domain_Models_LabelType = 'text' | 'textItem' | 'link' | 'image';

export interface General_TextLabelModel {
  key?: string | null;
  value?: string | null;
  url?: string | null;
  type: DC_Domain_Models_LabelType;
}

export type DC_Domain_Models_Usages_DynamicPricing_UsagesDataSource =
  | 'unknown'
  | 'classic'
  | 'dynamicPricing'
  | 'mixed';

export interface Usages_V3_Aggregation_AggregationPeriod {
  from: string;
  to: string;
}

export type Enums_Currency = 'EUR';

export interface DecimalObject {
  currency?: Enums_Currency;
  scale: number;
  value: number;
}

export type DC_Domain_Models_Usages_DynamicPricing_DynamicPriceComponentType =
  | 'unknown'
  | 'wholesale'
  | 'commercial'
  | 'energyTax'
  | 'fixedProductCost'
  | 'fixedDeliveryCosts'
  | 'fixedEnergyTaxReduction';

export type DC_Domain_Models_Usages_UsageInterval = 'Quarter' | 'Hour' | 'Day' | 'Week' | 'Month' | 'Year';

export interface Usages_V3_UsagesDynamicFixedCostDetail {
  amount?: DecimalObject;
  type: DC_Domain_Models_Usages_DynamicPricing_DynamicPriceComponentType;
  interval?: DC_Domain_Models_Usages_UsageInterval;
}

export interface Usages_V3_UsagesDynamicPriceDetail {
  amount?: DecimalObject;
  tariff?: DecimalObject;
  type: DC_Domain_Models_Usages_DynamicPricing_DynamicPriceComponentType;
}

export interface Usages_V3_Aggregation_UsagesTotal {
  high?: DecimalObject;
  highCostInclVat?: DecimalObject;
  low?: DecimalObject;
  lowCostInclVat?: DecimalObject;
  fixedCostInclVat?: DecimalObject;
  totalCostInclVat?: DecimalObject;
  totalUsageCostInclVat?: DecimalObject;
  variablePriceComponents?: Array<Usages_V3_UsagesDynamicPriceDetail> | null;
  fixedPriceComponents?: Array<Usages_V3_UsagesDynamicFixedCostDetail> | null;
  isMeterInError: boolean;
}

export interface Usages_V3_Aggregation_AggregationTotal {
  warmth?: Usages_V3_Aggregation_UsagesTotal;
  gas?: Usages_V3_Aggregation_UsagesTotal;
  electricity?: Usages_V3_Aggregation_UsagesTotal;
  redelivery?: Usages_V3_Aggregation_UsagesTotal;
  produced?: Usages_V3_Aggregation_UsagesTotal;
  tapWater?: Usages_V3_Aggregation_UsagesTotal;
  cooling?: Usages_V3_Aggregation_UsagesTotal;
  fixedCostInclVat?: DecimalObject;
  totalCosts?: DecimalObject;
  totalUsageCostInclVat?: DecimalObject;
}

export interface Usages_V3_Aggregation_AggregationSummary {
  aggregationTotals?: Usages_V3_Aggregation_AggregationTotal;
  aggregationTotalsPreviousYear?: Usages_V3_Aggregation_AggregationTotal;
}

export interface Usages_V3_Aggregation_Interval_IntervalBudget {
  warmth?: DecimalObject;
  gas?: DecimalObject;
  electricity?: DecimalObject;
  tapWater?: DecimalObject;
  cooling?: DecimalObject;
  total?: DecimalObject;
}

export type DC_Domain_Models_Usages_CollectorType =
  | 'Default'
  | 'Manual'
  | 'P1'
  | 'P4'
  | 'Interpolated'
  | 'Mixed'
  | 'Extrapolated'
  | 'NotMeasured'
  | 'MeterInError'
  | 'ProducedElectricity'
  | 'ExtrapolatedMixed'
  | 'MVS'
  | 'AdHoc'
  | 'AMR'
  | 'Correction'
  | 'Dispute'
  | 'EndOfSupply'
  | 'PhysicalReading'
  | 'MovingIn'
  | 'Periodic'
  | 'Switch'
  | 'TariffMigration'
  | 'MovingOut'
  | 'UsageCostOverview'
  | 'AdjustAllocationMethod'
  | 'MonthlyReading'
  | 'NewMeterInstallment'
  | 'OldMeterRemoval'
  | 'AdjustMeterConfig'
  | 'MonthlyEnergyReport'
  | 'DiverseReading'
  | 'Unknown';

export type DC_Domain_Models_Usages_UsageStatus = 'NOT_MEASURED' | 'MEASURED' | 'IN_ERROR';

export interface Usages_V3_Aggregation_Interval_UsageItem {
  status: DC_Domain_Models_Usages_UsageStatus;
  collectorType: DC_Domain_Models_Usages_CollectorType;
  source: DC_Domain_Models_Usages_DynamicPricing_UsagesDataSource;
  isDoubleTariff: boolean;
  isDoubleMeter: boolean;
  high?: DecimalObject;
  highCostInclVat?: DecimalObject;
  low?: DecimalObject;
  lowCostInclVat?: DecimalObject;
  lowRedeliveryCostsInclVat?: DecimalObject;
  lowCostWithoutRedeliveryCostsInclVat?: DecimalObject;
  highRedeliveryCostsInclVat?: DecimalObject;
  highCostWithoutRedeliveryCostsInclVat?: DecimalObject;
  fixedCostInclVat?: DecimalObject;
  totalCostInclVat?: DecimalObject;
  totalUsageCostInclVat?: DecimalObject;
  variablePriceComponents?: Array<Usages_V3_UsagesDynamicPriceDetail> | null;
  fixedPriceComponents?: Array<Usages_V3_UsagesDynamicFixedCostDetail> | null;
}

export interface Usages_V3_Aggregation_Interval_IntervalUsage {
  date: string;
  source: DC_Domain_Models_Usages_DynamicPricing_UsagesDataSource;
  warmth?: Usages_V3_Aggregation_Interval_UsageItem;
  gas?: Usages_V3_Aggregation_Interval_UsageItem;
  electricity?: Usages_V3_Aggregation_Interval_UsageItem;
  redelivery?: Usages_V3_Aggregation_Interval_UsageItem;
  produced?: Usages_V3_Aggregation_Interval_UsageItem;
  tapWater?: Usages_V3_Aggregation_Interval_UsageItem;
  cooling?: Usages_V3_Aggregation_Interval_UsageItem;
  totalCostInclVat?: DecimalObject;
  totalFixedCostInclVat?: DecimalObject;
  totalUsageCostInclVat?: DecimalObject;
}

export interface DC_Usages_Client_Models_Location {
  lat?: number | null;
  lon?: number | null;
  stationId?: string | null;
}

export interface Usages_V3_Aggregation_Interval_Weather_WeatherSunshineResult {
  date: string;
  sunshine?: DecimalObject;
}

export interface Usages_V3_Aggregation_Interval_Weather_WeatherTemperatureResult {
  date: string;
  temp?: DecimalObject;
}

export interface Usages_V3_Aggregation_Interval_Weather_WeatherDataModel {
  date: string;
  temp?: DecimalObject;
  location?: DC_Usages_Client_Models_Location;
  windSpeed?: DecimalObject;
  sunshine?: DecimalObject;
  coldestDay?: Usages_V3_Aggregation_Interval_Weather_WeatherTemperatureResult;
  warmestDay?: Usages_V3_Aggregation_Interval_Weather_WeatherTemperatureResult;
  sunniestDay?: Usages_V3_Aggregation_Interval_Weather_WeatherSunshineResult;
  radiation?: DecimalObject;
}

export interface Usages_V3_Aggregation_Interval_UsagesEntry {
  actual?: Usages_V3_Aggregation_Interval_IntervalUsage;
  previousYear?: Usages_V3_Aggregation_Interval_IntervalUsage;
  weather?: Usages_V3_Aggregation_Interval_Weather_WeatherDataModel;
  budget?: Usages_V3_Aggregation_Interval_IntervalBudget;
}

export interface Usages_V3_UsagesAggregation {
  source: DC_Domain_Models_Usages_DynamicPricing_UsagesDataSource;
  period?: Usages_V3_Aggregation_AggregationPeriod;
  entries?: Array<Usages_V3_Aggregation_Interval_UsagesEntry> | null;
  summary?: Usages_V3_Aggregation_AggregationSummary;
}

export type DC_Domain_Models_Usages_UsageAggregation = 'Hour' | 'Day' | 'Week' | 'Month' | 'Year' | 'Decade';

export interface Usages_V3_UsagesMetadata {
  interval: DC_Domain_Models_Usages_UsageInterval;
  aggregation: DC_Domain_Models_Usages_UsageAggregation;
}

export interface Usages_V3_UsagesModel {
  usageStatus?: Array<DC_Domain_Models_Usages_UsageStatusType> | null;
  metadata?: Usages_V3_UsagesMetadata;
  usages?: Array<Usages_V3_UsagesAggregation> | null;
  content?: Array<General_TextLabelModel> | null;
}

// Main UsagesResponse interface that wraps the data
export interface UsagesResponse {
  data: Usages_V3_UsagesModel;
}

// Common API Error Response
export interface ApiErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}
