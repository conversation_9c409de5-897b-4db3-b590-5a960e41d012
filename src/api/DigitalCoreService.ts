/**
 * Digital Core (DC) API Service
 *
 * This service provides methods to interact with the Digital Core API.
 */

import { apiClient } from './ApiClient';
import { MetersStatusResponse, ProfileResponse, UsagesResponse } from './types';

/**
 * Digital Core (DC) API Service
 */
export class DigitalCoreService {
  /**
   * Get the current authenticated user's profile
   * This method attempts to get the profile without requiring a customer ID
   * @returns The customer profile for the authenticated user
   */
  public async getCurrentUserProfile(): Promise<ProfileResponse> {
    console.log('Getting profile for current authenticated user');

    // Try common patterns for getting current user profile
    const possibleEndpoints = [
      '/eneco/me/profile',
      '/eneco/user/profile',
      '/eneco/profile',
      '/me/profile',
      '/user/profile',
      '/profile'
    ];

    let lastError: Error | null = null;

    for (const endpoint of possibleEndpoints) {
      try {
        console.log(`Trying endpoint: ${endpoint}`);
        const profile = await apiClient.request<ProfileResponse>(endpoint);
        console.log(`Successfully retrieved profile from: ${endpoint}`);
        return profile;
      } catch (error) {
        console.log(`Failed to get profile from ${endpoint}:`, error);
        lastError = error instanceof Error ? error : new Error(`Failed to fetch from ${endpoint}`);
        continue;
      }
    }

    // If all endpoints fail, throw the last error
    throw lastError || new Error('Unable to retrieve current user profile from any endpoint');
  }

  /**
   * Get customer ID from user information
   * This is a fallback method that might use user email or other identifiers
   * @param userEmail The user's email address
   * @returns The customer ID if found
   */
  public async getCustomerIdByEmail(userEmail: string): Promise<number> {
    console.log(`Attempting to get customer ID for email: ${userEmail}`);

    // Try common patterns for getting customer ID by email
    const possibleEndpoints = [
      `/eneco/customers/lookup?email=${encodeURIComponent(userEmail)}`,
      `/eneco/lookup/customer?email=${encodeURIComponent(userEmail)}`,
      `/customers/lookup?email=${encodeURIComponent(userEmail)}`,
      `/lookup/customer?email=${encodeURIComponent(userEmail)}`
    ];

    let lastError: Error | null = null;

    for (const endpoint of possibleEndpoints) {
      try {
        console.log(`Trying customer lookup endpoint: ${endpoint}`);
        const response = await apiClient.request<{ customerId: number } | { data: { customerId: number } }>(endpoint);

        // Handle different response formats
        const customerId = 'customerId' in response ? response.customerId : response.data?.customerId;

        if (customerId) {
          console.log(`Successfully retrieved customer ID: ${customerId}`);
          return customerId;
        }
      } catch (error) {
        console.log(`Failed to get customer ID from ${endpoint}:`, error);
        lastError = error instanceof Error ? error : new Error(`Failed to fetch from ${endpoint}`);
        continue;
      }
    }

    // If all endpoints fail, throw the last error
    throw lastError || new Error(`Unable to retrieve customer ID for email: ${userEmail}`);
  }

  /**
   * Get the customer profile by customer ID
   * @param customerId The customer ID
   * @returns The customer profile
   */
  public async getProfile(customerId: number): Promise<ProfileResponse> {
    console.log(`Getting profile for customer ID: ${customerId}`);

    // Use the exact path format from the working curl command
    return apiClient.request<ProfileResponse>(
      `/eneco/customers/${customerId}/profile`
    );
  }

  /**
   * Get the meters status
   * @param customerId The customer ID
   * @param accountId The account ID
   * @returns The meters status
   */
  public async getMetersStatus(customerId: number, accountId: number): Promise<MetersStatusResponse> {
    return apiClient.request<MetersStatusResponse>(
      `/nl/eneco/customers/${customerId}/accounts/${accountId}/meters/status`
    );
  }

  /**
   * Get the energy usages
   * @param customerId The customer ID
   * @param accountId The account ID
   * @param options The options for the request
   * @returns The energy usages
   */
  public async getUsages(
    customerId: number,
    accountId: number,
    options: {
      aggregation: 'Month' | 'Day' | 'Hour';
      interval: 'Month' | 'Day' | 'Hour';
      start: string; // ISO date string
      end: string; // ISO date string
      addBudget?: boolean;
      addWeather?: boolean;
      extrapolate?: boolean;
    }
  ): Promise<UsagesResponse> {
    // Build the query string
    const queryParams = new URLSearchParams({
      aggregation: options.aggregation,
      interval: options.interval,
      start: options.start,
      end: options.end,
      addBudget: options.addBudget?.toString() || 'false',
      addWeather: options.addWeather?.toString() || 'false',
      extrapolate: options.extrapolate?.toString() || 'false',
    });

    return apiClient.request<UsagesResponse>(
      `/nl/eneco/customers/${customerId}/accounts/${accountId}/usages?${queryParams.toString()}`
    );
  }
}

// Export a singleton instance
export const digitalCoreService = new DigitalCoreService();
