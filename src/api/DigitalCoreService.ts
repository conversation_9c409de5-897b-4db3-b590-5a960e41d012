/**
 * Digital Core (DC) API Service
 *
 * This service provides methods to interact with the Digital Core API.
 */

import { apiClient } from './ApiClient';
import { ProfileResponse, UsagesResponse } from './types';

/**
 * Digital Core (DC) API Service
 */
export class DigitalCoreService {


  /**
   * Get the customer profile by customer ID
   * @param customerId The customer ID
   * @returns The customer profile
   */
  public async getProfile(customerId: number): Promise<ProfileResponse> {
    console.log(`Getting profile for customer ID: ${customerId}`);

    // Use the correct endpoint format: /nl/eneco/customers/${customerId}/profile
    return apiClient.request<ProfileResponse>(
      `/eneco/customers/${customerId}/profile`
    );
  }

  /**
   * Get the energy usages
   * @param customerId The customer ID
   * @param accountId The account ID
   * @param options The options for the request
   * @returns The energy usages
   */
  public async getUsages(
    customerId: number,
    accountId: number,
    options: {
      aggregation: 'Month' | 'Day' | 'Hour';
      interval: 'Month' | 'Day' | 'Hour';
      start: string; // ISO date string
      end: string; // ISO date string
      addBudget?: boolean;
      addWeather?: boolean;
      extrapolate?: boolean;
    }
  ): Promise<UsagesResponse> {
    // Build the query string
    const queryParams = new URLSearchParams({
      aggregation: options.aggregation,
      interval: options.interval,
      start: options.start,
      end: options.end,
      addBudget: options.addBudget?.toString() || 'false',
      addWeather: options.addWeather?.toString() || 'false',
      extrapolate: options.extrapolate?.toString() || 'false',
    });

    return apiClient.request<UsagesResponse>(
      `/eneco/customers/${customerId}/accounts/${accountId}/usages?${queryParams.toString()}`
    );
  }
}

// Export a singleton instance
export const digitalCoreService = new DigitalCoreService();
