/**
 * Hook for using the Digital Core API
 */

import { useAuth } from '@/src/auth/AuthContext';
import { useCallback, useEffect, useState } from 'react';
import { digitalCoreService } from '../DigitalCoreService';
import { MetersStatusResponse, ProfileResponse, UsagesResponse } from '../types';

/**
 * Hook for using the Digital Core API
 */
export const useDigitalCore = () => {
  const { authState } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [customerId, setCustomerId] = useState<number | null>(null);

  // Fetch customer ID when user is authenticated
  useEffect(() => {
    const fetchCustomerId = async () => {
      if (!authState.isAuthenticated || !authState.user) {
        console.log('User not authenticated, clearing customer data');
        setCustomerId(null);
        return;
      }

      if (customerId) {
        console.log('Customer ID already available:', customerId);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log('Fetching customer profile for authenticated user:', authState.user.email);

        // First, try to get the profile directly
        try {
          const profile = await digitalCoreService.getCurrentUserProfile();

          if (profile?.data?.customerId) {
            setCustomerId(profile.data.customerId);
            console.log('Successfully retrieved customer ID from profile:', profile.data.customerId);
            return;
          }
        } catch (profileError) {
          console.log('Direct profile fetch failed, trying customer lookup by email:', profileError);
        }

        // Fallback: try to get customer ID by email
        if (authState.user.email) {
          try {
            const customerIdFromEmail = await digitalCoreService.getCustomerIdByEmail(authState.user.email);
            setCustomerId(customerIdFromEmail);
            console.log('Successfully retrieved customer ID from email lookup:', customerIdFromEmail);
            return;
          } catch (emailError) {
            console.log('Email lookup failed:', emailError);
          }
        }

        // If both methods fail, throw an error
        throw new Error('Unable to retrieve customer ID using any available method');
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Failed to fetch customer ID');
        setError(error);
        console.error('Error fetching customer ID:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomerId();
  }, [authState.isAuthenticated, authState.user, customerId]);

  /**
   * Manually refresh customer ID (useful for debugging or retry scenarios)
   */
  const refreshCustomerId = useCallback(async () => {
    setCustomerId(null);
    // This will trigger the useEffect to fetch the customer ID again
  }, []);

  /**
   * Manually set customer ID (useful for development/testing)
   */
  const setManualCustomerId = useCallback((id: number) => {
    console.log('Manually setting customer ID:', id);
    setCustomerId(id);
    setError(null);
  }, []);

  /**
   * Get the customer profile
   */
  const getProfile = useCallback(async (): Promise<ProfileResponse | null> => {
    if (!customerId) {
      const error = new Error('Customer ID not available. User may not be authenticated or customer data not loaded.');
      setError(error);
      console.error('Cannot fetch profile: Customer ID is null');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const profile = await digitalCoreService.getProfile(customerId);
      console.log("Profile data structure:", JSON.stringify(profile, null, 2));
      return profile;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch profile');
      setError(error);
      console.error('Error fetching profile:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [customerId]);

  /**
   * Get the meters status
   * @param accountId The account ID
   */
  const getMetersStatus = useCallback(async (accountId: number): Promise<MetersStatusResponse | null> => {
    if (!customerId) {
      const error = new Error('Customer ID not available. User may not be authenticated or customer data not loaded.');
      setError(error);
      console.error('Cannot fetch meters status: Customer ID is null');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const metersStatus = await digitalCoreService.getMetersStatus(customerId, accountId);
      return metersStatus;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch meters status');
      setError(error);
      console.error('Error fetching meters status:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [customerId]);

  /**
   * Get the energy usages
   * @param accountId The account ID
   * @param options The options for the request
   */
  const getUsages = useCallback(async (
    accountId: number,
    options: {
      aggregation: 'Month' | 'Day' | 'Hour';
      interval: 'Month' | 'Day' | 'Hour';
      start: string; // ISO date string
      end: string; // ISO date string
      addBudget?: boolean;
      addWeather?: boolean;
      extrapolate?: boolean;
    }
  ): Promise<UsagesResponse | null> => {
    if (!customerId) {
      const error = new Error('Customer ID not available. User may not be authenticated or customer data not loaded.');
      setError(error);
      console.error('Cannot fetch usages: Customer ID is null');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const usages = await digitalCoreService.getUsages(customerId, accountId, options);
      return usages;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch usages');
      setError(error);
      console.error('Error fetching usages:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [customerId]);

  return {
    isLoading,
    error,
    customerId,
    isAuthenticated: authState.isAuthenticated,
    getProfile,
    getMetersStatus,
    getUsages,
    refreshCustomerId,
    setManualCustomerId,
  };
};
