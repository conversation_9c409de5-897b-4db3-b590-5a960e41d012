/**
 * Hook for using the Digital Core API
 */

import { useAuth } from '@/src/auth/AuthContext';
import { useCallback, useEffect, useState } from 'react';
import { digitalCoreService } from '../DigitalCoreService';
import { ProfileResponse, UsagesResponse } from '../types';

/**
 * Hook for using the Digital Core API
 */
export const useDigitalCore = () => {
  const { authState } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [customerId, setCustomerId] = useState<number | null>(null);

  // Extract customer ID from authenticated user session
  useEffect(() => {
    if (!authState.isAuthenticated || !authState.user) {
      console.log('User not authenticated, clearing customer data');
      setCustomerId(null);
      setError(null);
      return;
    }

    // Debug: Log all available user data to see what claims are available
    console.log('Full user data from authentication session:', authState.user);

    // Try to get customer ID from the user profile (JWT token claims)
    // Try multiple possible claim names
    const userCustomerId = authState.user.customerId ||
                          authState.user.customer_id ||
                          (authState.user as any).customerNumber ||
                          (authState.user as any).customer_number ||
                          (authState.user as any).cid ||
                          (authState.user as any).customer ||
                          (authState.user as any).eneco_customer_id;

    console.log('Extracted customer ID:', userCustomerId);

    if (userCustomerId) {
      console.log('Customer ID found in user session:', userCustomerId);
      setCustomerId(userCustomerId);
      setError(null);
    } else {
      console.error('Customer ID not found in user session. Available user data:', authState.user);
      console.warn('TEMPORARY FALLBACK: Using hardcoded customer ID for development');

      // Temporary fallback to hardcoded customer ID while we debug the JWT claims
      const fallbackCustomerId = 14911126;
      setCustomerId(fallbackCustomerId);
      setError(null);

      console.log('Using fallback customer ID:', fallbackCustomerId);
      console.log('To fix this permanently, ensure the JWT token includes customer ID in one of these claim names:');
      console.log('- customerId, customer_id, customerNumber, customer_number, cid, customer, eneco_customer_id');
    }
  }, [authState.isAuthenticated, authState.user]);

  /**
   * Manually set customer ID (useful for development/testing when customer ID is not in JWT)
   */
  const setManualCustomerId = useCallback((id: number) => {
    console.log('Manually setting customer ID:', id);
    setCustomerId(id);
    setError(null);
  }, []);

  /**
   * Get the customer profile
   */
  const getProfile = useCallback(async (): Promise<ProfileResponse | null> => {
    if (!customerId) {
      const error = new Error('Customer ID not available. User may not be authenticated or customer data not loaded.');
      setError(error);
      console.error('Cannot fetch profile: Customer ID is null');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const profile = await digitalCoreService.getProfile(customerId);
      console.log("Profile data structure:", JSON.stringify(profile, null, 2));
      return profile;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch profile');
      setError(error);
      console.error('Error fetching profile:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [customerId]);

  /**
   * Get the energy usages
   * @param accountId The account ID
   * @param options The options for the request
   */
  const getUsages = useCallback(async (
    accountId: number,
    options: {
      aggregation: 'Month' | 'Day' | 'Hour';
      interval: 'Month' | 'Day' | 'Hour';
      start: string; // ISO date string
      end: string; // ISO date string
      addBudget?: boolean;
      addWeather?: boolean;
      extrapolate?: boolean;
    }
  ): Promise<UsagesResponse | null> => {
    if (!customerId) {
      const error = new Error('Customer ID not available. User may not be authenticated or customer data not loaded.');
      setError(error);
      console.error('Cannot fetch usages: Customer ID is null');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const usages = await digitalCoreService.getUsages(customerId, accountId, options);
      return usages;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch usages');
      setError(error);
      console.error('Error fetching usages:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [customerId]);

  return {
    isLoading,
    error,
    customerId,
    isAuthenticated: authState.isAuthenticated,
    getProfile,
    getUsages,
    setManualCustomerId,
  };
};
